#!/usr/bin/env python
"""
Test script to debug the BookForm
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'library_management.settings')
django.setup()

from library.forms import BookForm
from library.models import Author, Category

def test_book_form():
    """Test the BookForm to see what's happening"""
    print("🔍 Testing BookForm...")
    
    # Create a new form (like in the add view)
    form = BookForm()
    
    print(f"\n📝 Form authors field:")
    print(f"   - Type: {type(form.fields['authors'])}")
    print(f"   - Queryset count: {form.fields['authors'].queryset.count()}")
    print(f"   - Widget: {type(form.fields['authors'].widget)}")
    print(f"   - Required: {form.fields['authors'].required}")
    
    print(f"\n📝 Form categories field:")
    print(f"   - Type: {type(form.fields['categories'])}")
    print(f"   - Queryset count: {form.fields['categories'].queryset.count()}")
    print(f"   - Widget: {type(form.fields['categories'].widget)}")
    print(f"   - Required: {form.fields['categories'].required}")
    
    print(f"\n📝 Form authors value:")
    print(f"   - Value: {form['authors'].value()}")
    print(f"   - Type: {type(form['authors'].value())}")
    
    print(f"\n📝 Form categories value:")
    print(f"   - Value: {form['categories'].value()}")
    print(f"   - Type: {type(form['categories'].value())}")
    
    print(f"\n📝 Authors queryset:")
    authors = form.fields['authors'].queryset
    for i, author in enumerate(authors[:5]):
        print(f"   - Author {i+1}: {author.get_full_name()} (ID: {author.id})")
    
    print(f"\n📝 Categories queryset:")
    categories = form.fields['categories'].queryset
    for i, category in enumerate(categories[:5]):
        print(f"   - Category {i+1}: {category.name} (ID: {category.id})")
    
    # Test with some data
    print(f"\n📝 Testing form with data:")
    test_data = {
        'title': 'Test Book',
        'authors': [str(authors[0].id)] if authors else [],
        'categories': [str(categories[0].id)] if categories else [],
        'total_copies': 1,
        'isbn': 'TEST-123',
        'language': 'English'
    }
    
    form_with_data = BookForm(data=test_data)
    print(f"   - Form valid: {form_with_data.is_valid()}")
    if not form_with_data.is_valid():
        print(f"   - Errors: {form_with_data.errors}")
    
    print(f"   - Authors value with data: {form_with_data['authors'].value()}")
    print(f"   - Categories value with data: {form_with_data['categories'].value()}")

def main():
    """Run the test"""
    print("🚀 Starting BookForm Debug Test...\n")
    
    test_book_form()
    
    print("\n✅ Test completed!")

if __name__ == '__main__':
    main()
