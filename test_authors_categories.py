#!/usr/bin/env python
"""
Test script to check authors and categories in the database
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'library_management.settings')
django.setup()

from library.models import Author, Category

def check_authors_categories():
    """Check if there are authors and categories in the database"""
    print("🔍 Checking Authors and Categories in Database...")
    
    # Check Authors
    authors = Author.objects.all()
    print(f"\n📚 Authors in database: {authors.count()}")
    
    if authors.exists():
        print("✅ Authors found:")
        for author in authors[:10]:  # Show first 10
            print(f"   - {author.get_full_name()} (ID: {author.id})")
        if authors.count() > 10:
            print(f"   ... and {authors.count() - 10} more")
    else:
        print("❌ No authors found in database")
        print("💡 You need to add authors before you can add books")
        
        # Create a sample author
        try:
            sample_author = Author.objects.create(
                first_name="<PERSON><PERSON>",
                last_name="Author",
                biography="A sample author for testing",
                nationality="Unknown"
            )
            print(f"✅ Created sample author: {sample_author.get_full_name()}")
        except Exception as e:
            print(f"❌ Error creating sample author: {e}")
    
    # Check Categories
    categories = Category.objects.all()
    print(f"\n📂 Categories in database: {categories.count()}")
    
    if categories.exists():
        print("✅ Categories found:")
        for category in categories[:10]:  # Show first 10
            print(f"   - {category.name} (ID: {category.id})")
        if categories.count() > 10:
            print(f"   ... and {categories.count() - 10} more")
    else:
        print("❌ No categories found in database")
        print("💡 You need to add categories before you can add books")
        
        # Create sample categories
        sample_categories = [
            {"name": "Fiction", "description": "Fictional literature"},
            {"name": "Non-Fiction", "description": "Non-fictional works"},
            {"name": "Science", "description": "Scientific literature"},
            {"name": "History", "description": "Historical books"},
            {"name": "Technology", "description": "Technology and computing books"}
        ]
        
        try:
            for cat_data in sample_categories:
                category, created = Category.objects.get_or_create(
                    name=cat_data["name"],
                    defaults={"description": cat_data["description"]}
                )
                if created:
                    print(f"✅ Created sample category: {category.name}")
                else:
                    print(f"📝 Category already exists: {category.name}")
        except Exception as e:
            print(f"❌ Error creating sample categories: {e}")

def main():
    """Run the check"""
    print("🚀 Starting Authors and Categories Check...\n")
    
    check_authors_categories()
    
    print("\n✅ Check completed!")
    print("\n💡 Next steps:")
    print("   1. Go to /authors/add/ to add more authors")
    print("   2. Go to /categories/add/ to add more categories")
    print("   3. Then try adding a book at /books/add/")

if __name__ == '__main__':
    main()
