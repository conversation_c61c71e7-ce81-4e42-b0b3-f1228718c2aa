{% extends 'base.html' %}

{% block title %}{{ title }} - LibraryPro{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto animate-fade-in">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-4xl font-bold text-gray-800 mb-2">{{ title }}</h1>
        <p class="text-gray-600">Create a new book loan for a library member</p>
    </div>

    <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
        <!-- Form Header -->
        <div class="bg-gradient-to-r from-blue-50 to-purple-50 px-8 py-6 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-800 flex items-center">
                <i class="fas fa-hand-holding text-blue-600 mr-3"></i>
                Loan Information
            </h2>
        </div>

        <!-- Form Content -->
        <form method="post" class="p-8 space-y-8">
            {% csrf_token %}
            
            <!-- Book Selection -->
            <div class="space-y-4">
                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-book text-green-600 mr-2"></i>
                    Select Book
                </h3>
                <div class="grid grid-cols-1 gap-4">
                    <div>
                        <label for="{{ form.book.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Available Books *
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 flex items-center pointer-events-none z-10" style="padding-left: 1rem;">
                                <i class="fas fa-search text-gray-400 text-sm"></i>
                            </div>
                            <input type="text" id="book-search"
                                   class="w-full pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                                   placeholder="Type to search books by title or ISBN..." style="padding-left: 3rem;">
                            <input type="hidden" name="{{ form.book.name }}" id="{{ form.book.id_for_label }}" required>
                        </div>
                        <div id="book-results" class="hidden mt-2 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto z-20"></div>
                        {% if form.book.errors %}
                        <div class="mt-2 text-sm text-red-600">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ form.book.errors.0 }}
                        </div>
                        {% endif %}
                        
                        <!-- Book Info Display -->
                        <div id="bookInfo" class="mt-4 p-4 bg-gray-50 rounded-xl hidden">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                <div>
                                    <span class="font-medium text-gray-700">ISBN:</span>
                                    <span id="bookISBN" class="text-gray-600 ml-1">-</span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-700">Location:</span>
                                    <span id="bookLocation" class="text-gray-600 ml-1">-</span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-700">Available:</span>
                                    <span id="bookCopies" class="text-gray-600 ml-1">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Borrower Selection -->
            <div class="space-y-4">
                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-user text-purple-600 mr-2"></i>
                    Select Borrower
                </h3>
                <div class="grid grid-cols-1 gap-4">
                    <div>
                        <label for="{{ form.borrower.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Library Members *
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 flex items-center pointer-events-none z-10" style="padding-left: 1rem;">
                                <i class="fas fa-search text-gray-400 text-sm"></i>
                            </div>
                            <input type="text" id="borrower-search"
                                   class="w-full pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                                   placeholder="Type to search users by name, username, or ID..." style="padding-left: 3rem;">
                            <input type="hidden" name="{{ form.borrower.name }}" id="{{ form.borrower.id_for_label }}" required>
                        </div>
                        <div id="borrower-results" class="hidden mt-2 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto z-20"></div>
                        {% if form.borrower.errors %}
                        <div class="mt-2 text-sm text-red-600">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ form.borrower.errors.0 }}
                        </div>
                        {% endif %}
                        
                        <!-- Borrower Info Display -->
                        <div id="borrowerInfo" class="mt-4 p-4 bg-gray-50 rounded-xl hidden">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                <div>
                                    <span class="font-medium text-gray-700">Role:</span>
                                    <span id="borrowerRole" class="text-gray-600 ml-1">-</span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-700">Email:</span>
                                    <span id="borrowerEmail" class="text-gray-600 ml-1">-</span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-700">ID:</span>
                                    <span id="borrowerEnrollment" class="text-gray-600 ml-1">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loan Details -->
            <div class="space-y-4">
                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-calendar text-orange-600 mr-2"></i>
                    Loan Details
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="{{ form.due_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Due Date *
                        </label>
                        <input type="datetime-local" 
                               name="{{ form.due_date.name }}" 
                               id="{{ form.due_date.id_for_label }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                               required>
                        {% if form.due_date.errors %}
                        <div class="mt-2 text-sm text-red-600">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ form.due_date.errors.0 }}
                        </div>
                        {% endif %}
                        <div class="mt-2 text-sm text-gray-500">
                            <i class="fas fa-info-circle mr-1"></i>
                            Default loan period is 14 days
                        </div>
                    </div>
                    
                    <div>
                        <label for="{{ form.notes.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Notes (Optional)
                        </label>
                        <textarea name="{{ form.notes.name }}" 
                                  id="{{ form.notes.id_for_label }}"
                                  rows="4"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                                  placeholder="Add any special notes about this loan..."></textarea>
                        {% if form.notes.errors %}
                        <div class="mt-2 text-sm text-red-600">
                            <i class="fas fa-exclamation-circle mr-1"></i>{{ form.notes.errors.0 }}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{% url 'loan_list' %}" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-8 py-3 rounded-xl font-medium transition-all duration-300">
                    <i class="fas fa-times mr-2"></i>Cancel
                </a>
                <button type="button" id="loan-submit-btn"
                        class="btn-primary px-8 py-3 rounded-xl text-white font-medium shadow-lg"
                        onclick="handleLoanSubmit()">
                    <i class="fas fa-hand-holding mr-2"></i><span id="loan-submit-text">Issue Loan</span>
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Set default due date (14 days from now)
document.addEventListener('DOMContentLoaded', function() {
    const dueDateInput = document.getElementById('{{ form.due_date.id_for_label }}');
    const now = new Date();
    now.setDate(now.getDate() + 14);

    // Format date for datetime-local input
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');

    dueDateInput.value = `${year}-${month}-${day}T${hours}:${minutes}`;

    // Form submission will be handled by handleLoanSubmit function
});

function updateBookInfo(selectOrData) {
    const bookInfo = document.getElementById('bookInfo');

    // Handle both select element and data object
    let selectedOption;
    if (selectOrData.options) {
        // It's a select element
        selectedOption = selectOrData.options[selectOrData.selectedIndex];
    } else if (selectOrData.selectedOptions && selectOrData.selectedOptions[0]) {
        // It's a data object with selectedOptions
        selectedOption = selectOrData.selectedOptions[0];
    } else {
        // Hide info if no valid data
        bookInfo.classList.add('hidden');
        return;
    }

    if (selectedOption && selectedOption.value) {
        document.getElementById('bookISBN').textContent = selectedOption.dataset.isbn || '-';
        document.getElementById('bookLocation').textContent = selectedOption.dataset.location || '-';
        document.getElementById('bookCopies').textContent = selectedOption.dataset.copies || '-';
        bookInfo.classList.remove('hidden');
    } else {
        bookInfo.classList.add('hidden');
    }
}

function updateBorrowerInfo(selectOrData) {
    const borrowerInfo = document.getElementById('borrowerInfo');

    // Handle both select element and data object
    let selectedOption;
    if (selectOrData.options) {
        // It's a select element
        selectedOption = selectOrData.options[selectOrData.selectedIndex];
    } else if (selectOrData.selectedOptions && selectOrData.selectedOptions[0]) {
        // It's a data object with selectedOptions
        selectedOption = selectOrData.selectedOptions[0];
    } else {
        // Hide info if no valid data
        borrowerInfo.classList.add('hidden');
        return;
    }

    if (selectedOption && selectedOption.value) {
        document.getElementById('borrowerRole').textContent = selectedOption.dataset.role || '-';
        document.getElementById('borrowerEmail').textContent = selectedOption.dataset.email || '-';
        document.getElementById('borrowerEnrollment').textContent = selectedOption.dataset.enrollment || '-';
        borrowerInfo.classList.remove('hidden');
    } else {
        borrowerInfo.classList.add('hidden');
    }
}

// Book autocomplete
let bookSearchTimeout;
const bookSearch = document.getElementById('book-search');
const bookResults = document.getElementById('book-results');
const bookInput = document.getElementById('{{ form.book.id_for_label }}');

bookSearch.addEventListener('input', function() {
    clearTimeout(bookSearchTimeout);
    const query = this.value.trim();

    if (query.length < 2) {
        bookResults.classList.add('hidden');
        return;
    }

    bookSearchTimeout = setTimeout(() => {
        fetch(`{% url 'book_autocomplete' %}?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                bookResults.innerHTML = '';

                if (data.length === 0) {
                    bookResults.innerHTML = '<div class="p-3 text-gray-500">No books found</div>';
                } else {
                    data.forEach(book => {
                        const div = document.createElement('div');
                        div.className = 'p-3 hover:bg-gray-100 cursor-pointer border-b border-gray-200 last:border-b-0';
                        div.innerHTML = `
                            <div class="font-medium text-gray-900">${book.title}</div>
                            <div class="text-sm text-gray-600">${book.authors}</div>
                            <div class="text-xs text-green-600">${book.available_copies} copies available</div>
                        `;
                        div.addEventListener('click', () => {
                            bookSearch.value = book.text;
                            bookInput.value = book.id;
                            bookResults.classList.add('hidden');
                            updateBookInfo({ selectedOptions: [{ dataset: { isbn: book.isbn, location: '', copies: book.available_copies } }] });
                        });
                        bookResults.appendChild(div);
                    });
                }

                bookResults.classList.remove('hidden');
            })
            .catch(error => {
                console.error('Error fetching books:', error);
                bookResults.innerHTML = '<div class="p-3 text-red-500">Error loading books</div>';
                bookResults.classList.remove('hidden');
            });
    }, 300);
});

// Borrower autocomplete
let borrowerSearchTimeout;
const borrowerSearch = document.getElementById('borrower-search');
const borrowerResults = document.getElementById('borrower-results');
const borrowerInput = document.getElementById('{{ form.borrower.id_for_label }}');

borrowerSearch.addEventListener('input', function() {
    clearTimeout(borrowerSearchTimeout);
    const query = this.value.trim();

    if (query.length < 2) {
        borrowerResults.classList.add('hidden');
        return;
    }

    borrowerSearchTimeout = setTimeout(() => {
        fetch(`{% url 'user_autocomplete' %}?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                borrowerResults.innerHTML = '';

                if (data.length === 0) {
                    borrowerResults.innerHTML = '<div class="p-3 text-gray-500">No users found</div>';
                } else {
                    data.forEach(user => {
                        const div = document.createElement('div');
                        div.className = 'p-3 hover:bg-gray-100 cursor-pointer border-b border-gray-200 last:border-b-0';
                        div.innerHTML = `
                            <div class="font-medium text-gray-900">${user.name}</div>
                            <div class="text-sm text-gray-600">${user.username} - ${user.role}</div>
                            ${user.enrollment_number ? `<div class="text-xs text-blue-600">ID: ${user.enrollment_number}</div>` : ''}
                        `;
                        div.addEventListener('click', () => {
                            borrowerSearch.value = user.text;
                            borrowerInput.value = user.id;
                            borrowerResults.classList.add('hidden');
                            updateBorrowerInfo({ selectedOptions: [{ dataset: { role: user.role, email: '', enrollment: user.enrollment_number } }] });
                        });
                        borrowerResults.appendChild(div);
                    });
                }

                borrowerResults.classList.remove('hidden');
            })
            .catch(error => {
                console.error('Error fetching users:', error);
                borrowerResults.innerHTML = '<div class="p-3 text-red-500">Error loading users</div>';
                borrowerResults.classList.remove('hidden');
            });
    }, 300);
});

// Hide results when clicking outside
document.addEventListener('click', function(e) {
    if (!bookSearch.contains(e.target) && !bookResults.contains(e.target)) {
        bookResults.classList.add('hidden');
    }
    if (!borrowerSearch.contains(e.target) && !borrowerResults.contains(e.target)) {
        borrowerResults.classList.add('hidden');
    }
});

// Loan form submission handler
function handleLoanSubmit() {
    console.log('🖱️ Loan submit button clicked!');

    // Validate required fields
    const bookInput = document.getElementById('{{ form.book.id_for_label }}');
    const borrowerInput = document.getElementById('{{ form.borrower.id_for_label }}');

    if (!bookInput || !borrowerInput || !bookInput.value || !borrowerInput.value) {
        alert('Please select both a book and a borrower before submitting.');
        return false;
    }

    console.log('⏳ Showing loading state...');

    // Show loading state
    const submitBtn = document.getElementById('loan-submit-btn');
    const submitText = document.getElementById('loan-submit-text');

    if (submitBtn && submitText) {
        submitBtn.disabled = true;
        submitText.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
        console.log('✅ Loading state applied');
    }

    console.log('🚀 Submitting loan form...');

    // Submit the form directly
    const form = document.querySelector('form');
    if (form) {
        form.submit();
    } else {
        console.log('❌ Form not found');
    }

    return false;
}

console.log('🔍 Loan form script loaded');
</script>
{% endblock %}
